export USER_ID = $(shell id -u)
export GROUP_ID = $(shell id -g)
SHELL := /bin/bash

BASE_DIR := $(shell dirname $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST)))))
DOCKER_COMPOSE_ENV := env USER_ID=$(USER_ID) env GROUP_ID=$(GROUP_ID)
DOCKER_COMPOSE_BASE := $(DOCKER_COMPOSE_ENV) docker compose
DOCKER_COMPOSE := $(DOCKER_COMPOSE_BASE) -f $(BASE_DIR)/stack/docker-compose.yml

PROJECT_NAME != $(DOCKER_COMPOSE) config --format json | jq -r .name
EXEC_CONTAINER_NAME != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.main"))) | .[].key'
PG_DB_SERVICE != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.pg"))) | .[].key'
PG_DB_NAME != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.pg"))) | .[].value.labels."phcstack.pg"'

## Only active volumes (used in a container) will be listed
DOCKER_VOLUMES != $(DOCKER_COMPOSE) config --format json | jq -r '. | select(has("volumes")) | .volumes | to_entries | select(.[].value.external=true) | .[].value.name'

OK_COLOR=\033[1;92m
KO_COLOR=\033[1;91m
WARN_COLOR=\033[0;93m
INFO_COLOR=\033[1;94m
NO_COLOR=\033[0m
