export USER_ID = $(shell id -u)
export GROUP_ID = $(shell id -g)
SHELL := /bin/bash

CURRENT_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))

include $(CURRENT_DIR)/common.mk

.PHONY: before-install
before-install: purge start ## Common action to run before installing the app

.PHONY: start
start: create-volumes required-dependencies ##@ Démarre les conteneurs du projet
	@$(DOCKER_COMPOSE) up -d

.PHONY: required-dependencies
required-dependencies:: # (Optionnel) Démarre les dépendances requises (mais ne les installent pas). Ex: BDD
	@true

.PHONY: external-dependencies
external-dependencies:: # (Optionnel) Démarre les dépendances externes (mais ne les installent pas)
	@true

.PHONY: stop
stop: ##@ Stoppe les conteneurs du projet
	@$(DOCKER_COMPOSE) stop

.PHONY: clean
clean: ##@ Stoppe et supprime les conteneurs (Ne supprime pas les volumes de données)
	@$(DOCKER_COMPOSE) down

.PHONY: purge
purge: clean clean-volumes ##@ Stoppe et supprime les conteneurs (Supprime les volumes de données)
	@echo $(EXEC_CONTAINER_NAME)

.PHONY: create-volumes
create-volumes: ##@ Vérifie que les volumes de données existe. Création desdits volumes si nécessaires
	@for i in $(DOCKER_VOLUMES); do \
		if docker volume ls --format json | jq -se --arg i $$i 'map(select(.Name == $$i)) | .[0].Name == $$i' > /dev/null; \
		then \
			printf " $(WARN_COLOR)❯$(NO_COLOR) Docker volume $$i already exists\n"; \
		else \
			printf " $(OK_COLOR)❯$(NO_COLOR) Creating docker volume\n"; \
			docker volume create --name=$$i; \
		fi \
	done;

.PHONY: clean-volumes
clean-volumes: ##@ Suppression des volumes de données du projet
	@for i in $(DOCKER_VOLUMES); do \
		docker volume rm $$i -f; \
	done;

.PHONY: exec
exec: exec-help ##@ Ouvre une session CLI dans le conteneur docker "main". Le "main" est précisé dans les labels du fichier docker-compose principal: "- phcstack.main=true" (doit être unique).
	@$(DOCKER_COMPOSE) exec -it $(EXEC_CONTAINER_NAME) bash

.PHONY: exec-help
exec-help:: # (Optionnel) Recette jouée avant la target exec pour afficher une aide contextuel
	@true

##@
##@ Recipes for docker stack (There should be no reason to use them directly)
##@

.PHONY: registry-aware-services
registry-aware-services: ## List all the services (as json) that can be managed via phcstack for build/push to our docker registry. Uses the project labels: "- phcstack.registry=true" (Can be put on multiple services).
	@$(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.registry"))) | map({ label: .key, value: .key, hint: .value.labels."phcstack.registry" })'

.PHONY: recipes-to-json
recipes-to-json: ## Print recipes as json (formatted for select options in clack/prompt)
	@grep -F -he "##@" $(MAKEFILE_LIST) | grep -F -v grep -F | sed -e 's/\\$$//' | awk 'BEGIN {FS = ":*[[:space:]]*##@[[:space:]]*"}; \
	{ \
		if($$2 == "") \
			pass; \
		else if($$0 ~ /^#/) \
			pass; \
		else if($$1 == "") \
			pass; \
		else \
			printf "%s###%s\n", $$1, $$2; \
	}' | head -c-1 | jq '. | split("\n") | map( split("###")  | { label: .[0] | sub(":.*"; ""), value: .[0] | sub(":.*"; ""), hint: .[1] })' -Rs

.PHONY: list-project-hosts
list-project-hosts: ## List all current project hosts configured in docker-compose.yml
	@$(DOCKER_COMPOSE) config --format json | \
		jq '.services | to_entries | map(select(.value?.labels)) | .[].value.labels | flatten' | \
		jq -s '. | add | map(select(. | startswith("Host"))) | map(split("||")) | flatten | unique | [.[] | sub(".*Host(SNI)?\\(`(?<a>.*)`\\).*"; "\(.a)")]'

.PHONY: update-self
update-self: ## This recipe is to be used if you need to update the phcstack helpers for the git subtree
	@git fetch phcstack-helpers main && git subtree pull --prefix .phcstack phcstack-helpers main --squash

##@
##@ Default
##@

# @see https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
# @see https://gist.github.com/prwhite/8168133?permalink_comment_id=4739597#gistcomment-4739597
.PHONY: help
help: ##@ Print recipes with their descriptions.
	@printf "\n Usage: make <command>\n\n"
	@grep -F -he "##@" $(MAKEFILE_LIST) | grep -F -v grep -F | sed -e 's/\\$$//' | awk 'BEGIN {FS = ":*[[:space:]]*##@[[:space:]]*"}; \
	{ \
		if($$2 == "") \
			pass; \
		else if($$0 ~ /^#/) \
			printf "\n %s\n\n", $$2; \
		else if($$1 == "") \
			printf "     %-30s%s\n", "", $$2; \
		else \
			printf "    \033[34m%-30s\033[0m %s\n", $$1, $$2; \
	}'

.DEFAULT_GOAL := help
