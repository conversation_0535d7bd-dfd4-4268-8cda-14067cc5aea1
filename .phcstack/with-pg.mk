CURRENT_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))

include $(CURRENT_DIR)/common.mk

.PHONY: pg-wait-for-it
pg-wait-for-it:
	@echo "Waiting for db to finish starting up..."
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) timeout 90s bash -c "until pg_isready -h $(PG_DB_SERVICE) ; do sleep 5 ; done"

.PHONY: before-pg-create-db
before-pg-create-db: pg-wait-for-it
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) psql -h $(PG_DB_SERVICE) -U postgres --command "select pg_terminate_backend(pid) from pg_stat_activity where datname = '$(PG_DB_NAME)' and pid <> pg_backend_pid();"
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) dropdb -h $(PG_DB_SERVICE) -U postgres --if-exists $(PG_DB_NAME)
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) createdb -h $(PG_DB_SERVICE) -U postgres -T svd_dev -O svd_dev $(PG_DB_NAME)

.PHONY: before-pg-create-db-test
before-pg-create-db-test: pg-wait-for-it
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) psql -h $(PG_DB_SERVICE) -U postgres --command "select pg_terminate_backend(pid) from pg_stat_activity where datname = '$(PG_DB_NAME)_test' and pid <> pg_backend_pid();"
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) dropdb -h $(PG_DB_SERVICE) -U postgres --if-exists $(PG_DB_NAME)_test
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) createdb -h $(PG_DB_SERVICE) -U postgres -T svd_dev -O svd_dev $(PG_DB_NAME)_test

.PHONY: before-pg-create-db-ref
before-pg-create-db-ref: pg-wait-for-it
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) psql -h $(PG_DB_SERVICE) -U postgres --command "select pg_terminate_backend(pid) from pg_stat_activity where datname = '$(PG_DB_NAME)_ref' and pid <> pg_backend_pid();"
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) dropdb -h $(PG_DB_SERVICE) -U postgres --if-exists $(PG_DB_NAME)_ref
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) createdb -h $(PG_DB_SERVICE) -U postgres -T svd_dev -O svd_dev $(PG_DB_NAME)_ref

.PHONY: remove-pgtap
remove-pgtap: ##@ Commande psql jouée pour supprimé l'extension pgtap de la bdd de ref
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) psql --host $(PG_DB_SERVICE) -U postgres $(PG_DB_NAME)_ref -c "DROP EXTENSION IF EXISTS pgtap;"

.PHONY: add-pgtap
add-pgtap: ##@ Commande psql jouée pour créer l'extension pgtap sur la bdd de ref
	@docker exec -e PGPASSWORD="postgres" -i $(EXEC_CONTAINER_NAME) psql --host $(PG_DB_SERVICE) -U postgres $(PG_DB_NAME)_ref -c "CREATE EXTENSION IF NOT EXISTS pgtap schema public;"
