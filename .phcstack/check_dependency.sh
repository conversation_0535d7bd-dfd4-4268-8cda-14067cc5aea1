#!/bin/bash

# Functions
ok() { echo -e '\e[1;32m'$1'\e[m'; } # Green
info() { echo -e '\e[1;94m'$1'\e[m'; } # Cyan
warn() { echo -e '\e[1;33m'$1'\e[m'; } # Orange

NAME=$1
SERVICE=$2
COMMAND=$3

if docker ps --format=json | jq -se --arg SERVICE $SERVICE 'map(select(.Names == $SERVICE)) | .[0].Names == $SERVICE' > /dev/null;
then
    echo "$(ok " ❯") $(warn "$NAME") is running"
else
    echo "$(warn " ❯ Starting $NAME")..."
    phcstack $COMMAND start
fi
