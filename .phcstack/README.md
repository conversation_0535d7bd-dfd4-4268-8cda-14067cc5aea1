# Phcstack Helpers

Helpers partagés entre les tous nos projets qui utilisent PHCSTACK.

- [Comment utiliser Git Subtree pour gérer plusieurs référentiels de projet](https://fr.linux-console.net/?p=7596)

## Ajout du repo dans le projet de destination

```bash

## Ajouter le remote des helpers dans le projet de destination
cd /path/to/project
git remote add -f phcstack-helpers **************:son-video/shared/phcstack-helpers.git

git checkout <branche en cours>
git subtree add --prefix .phcstack phcstack-helpers main --squash
```

## Récupération des modifs

```bash 
## Fetch les dernières modifications
git fetch phcstack-helpers main

## Les appliqué sur dans le sous-arbre avec un "squash" pour ne pas garder l'historique entier dans le projet de destination
git subtree pull --prefix .phcstack phcstack-helpers main --squash
```

## Pousser des modifs

> Ce n'est pas une solution à privilégier

```bash 
git subtree push --prefix .phcstack phcstack-helpers main
```
