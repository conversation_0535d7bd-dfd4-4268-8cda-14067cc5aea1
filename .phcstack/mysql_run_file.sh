#!/bin/bash

# Functions
ok() { echo -e '\e[32m'$1'\e[m'; } # Green
info() { echo -e '\e[36m'$1'\e[m'; } # Cyan
warn() { echo -e '\e[33m'$1'\e[m'; } # Orange
die() {
    echo -e '\e[1;31m'$1'\e[m'
    exit 1
} # Red

# also Red, but while running a command: https://serverfault.com/questions/59262/bash-print-stderr-in-red-color#answer-502019
try() (set -o pipefail;"$@" 2>&1>&3|sed $'s,.*,\e[31m&\e[m,'>&2)3>&1

START_TIME=$(date +%s)
elapsed() {
    TIME="$(($(date +%s) - ${START_TIME}))"
    info "[$(( ${TIME} / 3600 ))h $(( (${TIME} / 60) % 60 ))m $(( ${TIME} % 60 ))s]"
}

MAX_TRIES=$3
ATTEMPTS=0

if [ $MAX_TRIES -gt 0 ]; then
  echo
  echo "$(elapsed) $(ok "> ") Waiting for MySQL server to be available"
  echo "$(elapsed) $(ok "> ") Will attempt to run the file ${MAX_TRIES} times..."

  until [ $(($ATTEMPTS - 1)) -eq $MAX_TRIES ]; do
    docker exec -i $1 mysql -uroot -proot -e ';' && break
    ATTEMPTS=$(($ATTEMPTS + 1))
    echo "$(elapsed) $(warn "> ") MySQL server still unavailable, retries in 5sec..."
    sleep 5
  done

  if [ $(($ATTEMPTS - 1)) -eq $MAX_TRIES ]; then
    ATTEMPTS=$(($ATTEMPTS - 1))
    echo
    echo "$(elapsed) $(die " >>>") Error: unable to reach the MySQL server after ${ATTEMPTS} attempt(s)"
    die " "
    exit 1
  fi
fi

if [ ! -f "$2" ]; then
  echo
  echo "$(elapsed) $(die " >>>") Error: File does not exists in given target path: ${2}"
  exit 1
fi

echo
docker exec -i $1 mysql -uroot -proot < $2
echo "$(elapsed) $(ok " >") The file ${2} was successfully run against the MySQL database (${ATTEMPTS} retrie(s))"
