CURRENT_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))

include $(CURRENT_DIR)/common.mk

SYNAPPS_DB_SERVICE := "synapps-postgresql"
SYNAPPS_DB_NAME := "svd_synapps"
SYNAPPS_APP_NAME != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.synapps_app_name"))) | .[].value.labels."phcstack.synapps_app_name"'
SYNAPPS_APP_ID != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.synapps_app_id"))) | .[].value.labels."phcstack.synapps_app_id"'
SYNAPPS_APP_RPC != $(DOCKER_COMPOSE) config --format json | jq -r '.services | to_entries | map(select(.value?.labels | has("phcstack.synapps_app_rpc"))) | .[].value.labels."phcstack.synapps_app_rpc"'

.PHONY: synapps-register
synapps-register: ##@ Enregistre l'application dans la BDD de Synapps server
	@if [ -z $(SYNAPPS_APP_NAME) ]; \
	then \
		printf "$(WARN_COLOR)❯$(NO_COLOR) No synapps config found for the current project, check your docker labels\n"; \
	else \
		$(DOCKER_COMPOSE) exec -it $(EXEC_CONTAINER_NAME)  \
			psql \
			--host=$(SYNAPPS_DB_SERVICE) \
			--username=postgres \
			--dbname=$(SYNAPPS_DB_NAME) \
			--command="DELETE FROM synapps.client WHERE name = '$(SYNAPPS_APP_NAME)' OR client_id = '$(SYNAPPS_APP_ID)';"; \
		$(DOCKER_COMPOSE) exec -it $(EXEC_CONTAINER_NAME) \
			psql \
			--host=$(SYNAPPS_DB_SERVICE) \
			--username=postgres \
			--dbname=$(SYNAPPS_DB_NAME) \
			--command="INSERT INTO synapps.client (client_id, ip_address, name, last_registered_at, service_uri) VALUES ('$(SYNAPPS_APP_ID)', '127.0.0.1', '$(SYNAPPS_APP_NAME)', now(), '$(SYNAPPS_APP_RPC)');"; \
		printf "$(OK_COLOR)❯$(NO_COLOR) Application successfully registered in Synapps server for $(SYNAPPS_APP_NAME)\n"; \
		printf "$(OK_COLOR)❯$(NO_COLOR) Please check the value of SYNAPPS_CLIENT_ID in your project .env file (value for current application should be $(SYNAPPS_APP_ID))\n"; \
	fi

.PHONY: synapps-flush
synapps-flush: ## Flush synapps events
	@$(DOCKER_COMPOSE) exec -it $(EXEC_CONTAINER_NAME) psql -h $(PG_DB_SERVICE) -U postgres --dbname $(PG_DB_NAME) --command "TRUNCATE synapps_client.event_log"
	@$(DOCKER_COMPOSE) exec -it $(EXEC_CONTAINER_NAME) psql -h $(PG_DB_SERVICE) -U postgres --dbname $(PG_DB_NAME) --command "ALTER SEQUENCE synapps_client.message_seq RESTART WITH 1"
