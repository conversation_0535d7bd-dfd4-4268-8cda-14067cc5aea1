import asyncio
import os
import parlant.sdk as p
from dotenv import load_dotenv

async def main():

  print("Starting clippy...")
  load_env()

  async with p.Server(port=int(os.getenv("APP_PORT"))) as server:
    agent = await server.create_agent(
        name="Son-Vidéo IA",
        description="Tu es un conseiller Son-Vidéo.",
    )

def load_env():
  env = os.getenv("APP_ENV") or "dev"
  print(f"Loading env {env}")

  if env == "dev":
    load_dotenv(".env.local")
    load_dotenv(".env")
    return
  if env == "test":
    load_dotenv(".env.test")
    return

  load_dotenv(".env")

if __name__ == "__main__":
    asyncio.run(main())
