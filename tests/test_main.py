import pytest
import asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from app.main import main, load_env

@pytest.mark.asyncio
async def test_main_creates_agent():
    with patch('app.main.p.Server') as mock_server, \
         patch('app.main.os.getenv') as mock_getenv:

        # Mock environment variable
        mock_getenv.return_value = "8800"

        # Mock server instance
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance

        await main()

        # Verify server was created with correct port
        mock_server.assert_called_once_with(port=8800)

        # Verify agent was created with correct parameters
        mock_server_instance.create_agent.assert_called_once_with(
            name="Son-Vidéo IA",
            description="Tu es un conseiller Son-Vidéo."
        )


@pytest.mark.asyncio
async def test_main_with_different_port():
    """Test that main() uses the correct port from environment"""
    with patch('app.main.p.Server') as mock_server, \
         patch('app.main.os.getenv') as mock_getenv:

        # Mock environment variable with different port
        mock_getenv.return_value = "9000"

        # Mock server instance
        mock_server_instance = AsyncMock()
        mock_server.return_value.__aenter__.return_value = mock_server_instance

        await main()

        # Verify server was created with the mocked port
        mock_server.assert_called_once_with(port=9000)


def test_load_env_dev():
    """Test load_env function with dev environment"""
    with patch('app.main.os.getenv') as mock_getenv, \
         patch('app.main.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "dev"

        load_env()

        # Verify correct .env files are loaded for dev
        assert mock_load_dotenv.call_count == 2
        mock_load_dotenv.assert_any_call(".env.local")
        mock_load_dotenv.assert_any_call(".env")


def test_load_env_test():
    """Test load_env function with test environment"""
    with patch('app.main.os.getenv') as mock_getenv, \
         patch('app.main.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "test"

        load_env()

        # Verify correct .env file is loaded for test
        mock_load_dotenv.assert_called_once_with(".env.test")


def test_load_env_production():
    """Test load_env function with production environment"""
    with patch('app.main.os.getenv') as mock_getenv, \
         patch('app.main.load_dotenv') as mock_load_dotenv:

        mock_getenv.return_value = "prod"

        load_env()

        # Verify default .env file is loaded for production
        mock_load_dotenv.assert_called_once_with(".env")